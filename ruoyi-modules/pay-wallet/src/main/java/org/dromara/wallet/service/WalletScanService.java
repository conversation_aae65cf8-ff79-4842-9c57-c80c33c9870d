package org.dromara.wallet.service;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.redis.utils.RedisUtils;
import org.dromara.common.scanning.MagicianBlockchainScan;
import org.dromara.common.scanning.biz.thread.EventThreadPool;
import org.dromara.common.scanning.commons.config.rpcinit.impl.EthRpcInit;
import org.dromara.common.scanning.commons.config.rpcinit.impl.TronRpcInit;
import org.dromara.wallet.config.facade.EvmConfigFacade;
import org.dromara.wallet.config.facade.TronConfigFacade;
import org.dromara.wallet.wallet.helper.EvmHelper;
import org.dromara.wallet.wallet.helper.TronHttpApiHelper;
import org.dromara.wallet.wallet.monitor.evm.event.EvmAddressFilterEvent;
import org.dromara.wallet.wallet.monitor.evm.event.EvmBusinessProcessEvent;
import org.dromara.wallet.wallet.monitor.evm.event.EvmEventFilterEvent;
import org.dromara.wallet.wallet.monitor.evm.event.EvmScanProgressEvent;
import org.dromara.wallet.wallet.monitor.tron.event.*;
import org.redisson.api.RBucket;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigInteger;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 统一区块链扫描服务
 * 整合持续扫描和手动扫描功能，支持EVM链和TRON链
 *
 * <p>功能特性：</p>
 * <ul>
 *   <li>持续扫描：支持多链独立扫描管理，进度持久化</li>
 *   <li>手动扫描：支持交易哈希/ID触发扫描，区块范围扫描</li>
 *   <li>多链支持：EVM链（BSC、ARB、BASE）和TRON链</li>
 *   <li>统一管理：统一的状态管理和配置接口</li>
 * </ul>
 *
 * <AUTHOR>
 * @date 2025/7/30
 */
@Slf4j
@Service
public class WalletScanService {

    // ============ 依赖注入 ============

    private final TronConfigFacade tronConfigFacade;
    private final EvmHelper evmHelper;
    private final TronHttpApiHelper tronHttpApiHelper;

    // EVM事件处理器
    private final EvmAddressFilterEvent evmAddressFilterEvent;
    private final EvmEventFilterEvent evmEventFilterEvent;
    private final EvmBusinessProcessEvent evmBusinessProcessEvent;
    private final EvmScanProgressEvent evmScanProgressEvent;

    // TRON事件处理器
    private final TronAddressFilterEvent tronAddressFilterEvent;
    private final TronTransactionDetailEvent tronTransactionDetailEvent;
    private final TronEventFilterEvent tronEventFilterEvent;
    private final TronBusinessProcessEvent tronBusinessProcessEvent;
    private final TronScanProgressEvent tronScanProgressEvent;

    // ============ 持续扫描管理 ============

    /**
     * 多链扫描任务管理
     * Key: 链名称, Value: 扫描任务实例
     */
    private final ConcurrentHashMap<String, MagicianBlockchainScan> scanTasks = new ConcurrentHashMap<>();

    /**
     * 多链扫描状态管理
     * Key: 链名称, Value: 是否正在扫描
     */
    private final ConcurrentHashMap<String, AtomicBoolean> scanStates = new ConcurrentHashMap<>();

    /**
     * EVM链配置门面映射表
     */
    private final Map<String, EvmConfigFacade> evmConfigFacadeMap;

    /**
     * 构造函数，自动注入所有EvmConfigFacade实现并构建映射表
     */
    @Autowired
    public WalletScanService(TronConfigFacade tronConfigFacade,
                             EvmHelper evmHelper,
                             TronHttpApiHelper tronHttpApiHelper,
                             EvmAddressFilterEvent evmAddressFilterEvent,
                             EvmEventFilterEvent evmEventFilterEvent,
                             EvmBusinessProcessEvent evmBusinessProcessEvent,
                             EvmScanProgressEvent evmScanProgressEvent,
                             TronAddressFilterEvent tronAddressFilterEvent,
                             TronTransactionDetailEvent tronTransactionDetailEvent,
                             TronEventFilterEvent tronEventFilterEvent,
                             TronBusinessProcessEvent tronBusinessProcessEvent,
                             TronScanProgressEvent tronScanProgressEvent,
                             List<EvmConfigFacade> configFacades) {
        this.tronConfigFacade = tronConfigFacade;
        this.evmHelper = evmHelper;
        this.tronHttpApiHelper = tronHttpApiHelper;
        this.evmAddressFilterEvent = evmAddressFilterEvent;
        this.evmEventFilterEvent = evmEventFilterEvent;
        this.evmBusinessProcessEvent = evmBusinessProcessEvent;
        this.evmScanProgressEvent = evmScanProgressEvent;
        this.tronAddressFilterEvent = tronAddressFilterEvent;
        this.tronTransactionDetailEvent = tronTransactionDetailEvent;
        this.tronEventFilterEvent = tronEventFilterEvent;
        this.tronBusinessProcessEvent = tronBusinessProcessEvent;
        this.tronScanProgressEvent = tronScanProgressEvent;
        this.evmConfigFacadeMap = configFacades.stream()
            .collect(Collectors.toMap(
                EvmConfigFacade::getChainName,
                Function.identity()
            ));

        // 计算合理的线程池大小：EVM链数量 + TRON链(1) + 手动扫描预留(2)
        int totalThreads = evmConfigFacadeMap.size() + 1 + 2;
        EventThreadPool.init(totalThreads);

        log.info("EventThreadPool已统一初始化，线程池大小: {}", totalThreads);
        log.info("UnifiedBlockchainScanService初始化完成，支持的EVM链: {}, TRON支持: {}",
            evmConfigFacadeMap.keySet(), tronConfigFacade.isEnabled());
    }

    // ============ 持续扫描接口 ============

    /**
     * 启动EVM链的持续扫描任务
     */
    public boolean startEvmChainScan(EvmConfigFacade configFacade, BigInteger startBlock) {
        return startEvmChainScan(configFacade, startBlock, 10000L, true);
    }

    /**
     * 启动EVM链的持续扫描任务（完整参数）
     */
    public boolean startEvmChainScan(EvmConfigFacade configFacade, BigInteger startBlock,
                                     long scanPeriod, boolean enableLogScanning) {
        String chainName = configFacade.getChainName();

        // 获取或创建扫描状态
        AtomicBoolean scanState = scanStates.computeIfAbsent(chainName, k -> new AtomicBoolean(false));

        if (!scanState.compareAndSet(false, true)) {
            log.warn("{}链扫描任务已在运行中，无法重复启动", chainName);
            return false;
        }

        try {
            // 从Redis恢复扫描进度
            BigInteger resumeBlock = getLastScanBlock(chainName);
            if (resumeBlock != null && resumeBlock.compareTo(startBlock) > 0) {
                startBlock = resumeBlock.add(BigInteger.ONE);
                log.info("{}链从Redis恢复扫描进度，起始区块: {}", chainName, startBlock);
            }

            log.info("开始启动{}链持续扫描任务，起始区块: {}, 扫描周期: {}ms, 日志扫描: {}",
                chainName, startBlock, scanPeriod, enableLogScanning);

            // 创建EVM扫描任务
            MagicianBlockchainScan scanTask = MagicianBlockchainScan.create()
                .setRpcUrl(createEthRpcInit(configFacade))
                .setBeginBlockNumber(startBlock)
                .setScanPeriod(scanPeriod)
                // 按处理顺序添加监控事件
                .addEthMonitorEvent(evmAddressFilterEvent)      // 1. 地址预过滤
                .addEthMonitorEvent(evmScanProgressEvent)       // 2. 扫描进度保存
                .addEthMonitorEvent(evmEventFilterEvent)        // 3. 事件过滤
                .addEthMonitorEvent(evmBusinessProcessEvent);   // 4. 业务处理

            // 配置批量处理和日志扫描
            if (enableLogScanning) {
                scanTask.enableLogScanning(50, 3);
                log.info("{}链启用日志扫描模式，批量大小: 50, 并发线程: 3", chainName);
            } else {
                scanTask.enableBatchProcessing(50, 3);
                log.info("{}链启用批量处理模式，批量大小: 50, 并发线程: 3", chainName);
            }

            // 启动扫描任务
            scanTask.start();

            // 保存任务实例
            scanTasks.put(chainName, scanTask);

            log.info("{}链持续扫描任务启动成功", chainName);
            return true;

        } catch (Exception e) {
            log.error("{}链持续扫描任务启动失败: {}", chainName, e.getMessage(), e);
            scanState.set(false);
            return false;
        }
    }

    /**
     * 启动TRON链的持续扫描任务
     */
    public boolean startTronChainScan(BigInteger startBlock) {
        return startTronChainScan(startBlock, 15000L);
    }

    /**
     * 启动TRON链的持续扫描任务（完整参数）
     */
    public boolean startTronChainScan(BigInteger startBlock, long scanPeriod) {
        String chainName = "TRON";

        // 获取或创建扫描状态
        AtomicBoolean scanState = scanStates.computeIfAbsent(chainName, k -> new AtomicBoolean(false));

        if (!scanState.compareAndSet(false, true)) {
            log.warn("TRON链扫描任务已在运行中，无法重复启动");
            return false;
        }

        try {
            // 从Redis恢复扫描进度
            BigInteger resumeBlock = getLastScanBlock(chainName);
            if (resumeBlock != null && resumeBlock.compareTo(startBlock) > 0) {
                startBlock = resumeBlock.add(BigInteger.ONE);
                log.info("TRON链从Redis恢复扫描进度，起始区块: {}", startBlock);
            }

            log.info("开始启动TRON链持续扫描任务，起始区块: {}, 扫描周期: {}ms", startBlock, scanPeriod);

            // 创建TRON扫描任务
            MagicianBlockchainScan scanTask = MagicianBlockchainScan.create()
                .setRpcUrl(createTronRpcInit())
                .setBeginBlockNumber(startBlock)
                .setScanPeriod(scanPeriod)
                // 按处理顺序添加监控事件
                .addTronMonitorEvent(tronAddressFilterEvent)      // 1. 地址预过滤
                .addTronMonitorEvent(tronScanProgressEvent)       // 2. 扫描进度保存
                .addTronMonitorEvent(tronTransactionDetailEvent)  // 3. 获取交易详情
                .addTronMonitorEvent(tronEventFilterEvent)        // 4. 事件过滤
                .addTronMonitorEvent(tronBusinessProcessEvent);   // 5. 业务处理

            // 配置批量处理
            scanTask.enableBatchProcessing(10, 2);
            log.info("TRON链启用批量处理模式，批量大小: 10, 并发线程: 2");

            // 启动扫描任务
            scanTask.start();

            // 保存任务实例
            scanTasks.put(chainName, scanTask);

            log.info("TRON链持续扫描任务启动成功");
            return true;

        } catch (Exception e) {
            log.error("TRON链持续扫描任务启动失败: {}", e.getMessage(), e);
            scanState.set(false);
            return false;
        }
    }

    /**
     * 停止指定链的持续扫描任务
     */
    public boolean stopChainScan(String chainName) {
        AtomicBoolean scanState = scanStates.get(chainName);
        if (scanState == null || !scanState.get()) {
            log.warn("{}链扫描任务未在运行中，无需停止", chainName);
            return true;
        }

        try {
            MagicianBlockchainScan scanTask = scanTasks.get(chainName);
            if (scanTask != null) {
                // 在停止前尝试保存最后的扫描进度
                try {
                    BigInteger currentBlock = scanTask.getCurrentBlockHeight();
                    if (currentBlock != null) {
                        saveLastScanBlock(chainName, currentBlock);
                        log.info("{}链停止前保存最终扫描进度: {}", chainName, currentBlock);
                    }
                } catch (Exception e) {
                    log.warn("{}链停止前保存扫描进度失败: {}", chainName, e.getMessage());
                }

                scanTask.shutdown();
                scanTasks.remove(chainName);
                log.info("{}链持续扫描任务已停止", chainName);
            }
            return true;
        } catch (Exception e) {
            log.error("停止{}链持续扫描任务失败: {}", chainName, e.getMessage(), e);
            return false;
        } finally {
            scanState.set(false);
        }
    }

    /**
     * 停止所有持续扫描任务
     */
    public int stopAllScans() {
        int stoppedCount = 0;
        for (String chainName : scanTasks.keySet()) {
            if (stopChainScan(chainName)) {
                stoppedCount++;
            }
        }
        log.info("已停止{}个持续扫描任务", stoppedCount);
        return stoppedCount;
    }

    /**
     * 检查指定链是否正在持续扫描
     */
    public boolean isChainScanning(String chainName) {
        AtomicBoolean scanState = scanStates.get(chainName);
        return scanState != null && scanState.get();
    }

    /**
     * 获取指定链当前扫描的区块高度
     */
    public BigInteger getChainCurrentBlockHeight(String chainName) {
        MagicianBlockchainScan scanTask = scanTasks.get(chainName);
        if (scanTask != null && isChainScanning(chainName)) {
            return scanTask.getCurrentBlockHeight();
        }
        return null;
    }

    /**
     * 获取活跃的扫描链集合
     */
    public Set<String> getActiveScanChains() {
        return scanTasks.keySet();
    }

    // ============ 手动扫描接口 ============

    /**
     * 通过交易哈希触发EVM链区块扫描
     */
    public ScanResult scanEvmByTxHash(String chainName, String txHash, int contextBlocks, int timeoutSeconds) {
        try {
            log.info("开始通过交易哈希触发{}链区块扫描: {}", chainName, txHash);

            // 1. 验证链配置
            EvmConfigFacade configFacade = evmConfigFacadeMap.get(chainName);
            if (configFacade == null) {
                return ScanResult.failure("不支持的链类型: " + chainName);
            }

            if (!configFacade.isEnabled()) {
                return ScanResult.failure(chainName + "链配置未启用");
            }

            // 2. 通过交易哈希获取区块号
            BigInteger blockNumber = getBlockNumberByTxHash(txHash, configFacade);
            if (blockNumber == null) {
                return ScanResult.failure("交易不存在或未确认: " + txHash);
            }

            // 3. 计算扫描范围
            BigInteger fromBlock = blockNumber.subtract(BigInteger.valueOf(contextBlocks));
            BigInteger toBlock = blockNumber.add(BigInteger.valueOf(contextBlocks));

            // 确保不扫描负数区块
            if (fromBlock.compareTo(BigInteger.ZERO) < 0) {
                fromBlock = BigInteger.ZERO;
            }

            log.info("{}链交易{}位于区块{}，扫描范围: {} - {}",
                chainName, txHash, blockNumber, fromBlock, toBlock);

            // 4. 执行区块扫描
            return scanEvmBlockRange(chainName, fromBlock, toBlock, timeoutSeconds);

        } catch (Exception e) {
            log.error("{}链通过交易哈希{}触发扫描失败", chainName, txHash, e);
            return ScanResult.failure("扫描失败: " + e.getMessage());
        }
    }

    /**
     * 扫描EVM链指定区块范围
     */
    public ScanResult scanEvmBlockRange(String chainName, BigInteger fromBlock, BigInteger toBlock, int timeoutSeconds) {
        try {
            log.info("开始扫描{}链区块范围: {} - {}", chainName, fromBlock, toBlock);

            // 1. 验证链配置
            EvmConfigFacade configFacade = evmConfigFacadeMap.get(chainName);
            if (configFacade == null) {
                return ScanResult.failure("不支持的链类型: " + chainName);
            }

            if (!configFacade.isEnabled()) {
                return ScanResult.failure(chainName + "链配置未启用");
            }

            // 2. 验证区块范围
            if (fromBlock.compareTo(toBlock) > 0) {
                return ScanResult.failure("起始区块号不能大于结束区块号");
            }

            long blockCount = toBlock.subtract(fromBlock).longValue() + 1;
            if (blockCount > 100) {
                return ScanResult.failure("扫描区块数量不能超过100个，当前: " + blockCount);
            }

            // 3. 创建并启动扫描任务
            return executeEvmScan(configFacade, fromBlock, toBlock, timeoutSeconds);

        } catch (Exception e) {
            log.error("{}链扫描区块范围{} - {}失败", chainName, fromBlock, toBlock, e);
            return ScanResult.failure("扫描失败: " + e.getMessage());
        }
    }

    /**
     * 通过交易ID触发TRON链区块扫描
     */
    public ScanResult scanTronByTxId(String txId, int contextBlocks, int timeoutSeconds) {
        try {
            log.info("开始通过交易ID触发TRON链区块扫描: {}", txId);

            if (!tronConfigFacade.isEnabled()) {
                return ScanResult.failure("TRON链配置未启用");
            }

            // 通过交易ID获取区块号
            BigInteger blockNumber = getTronBlockNumberByTxId(txId);
            if (blockNumber == null) {
                return ScanResult.failure("交易不存在或未确认: " + txId);
            }

            // 计算扫描范围
            BigInteger fromBlock = blockNumber.subtract(BigInteger.valueOf(contextBlocks));
            BigInteger toBlock = blockNumber.add(BigInteger.valueOf(contextBlocks));

            // 确保不扫描负数区块
            if (fromBlock.compareTo(BigInteger.ZERO) < 0) {
                fromBlock = BigInteger.ZERO;
            }

            log.info("TRON链交易{}位于区块{}，扫描范围: {} - {}",
                txId, blockNumber, fromBlock, toBlock);

            // 执行区块扫描
            return scanTronBlockRange(fromBlock, toBlock, timeoutSeconds);

        } catch (Exception e) {
            log.error("TRON链通过交易ID{}触发扫描失败", txId, e);
            return ScanResult.failure("扫描失败: " + e.getMessage());
        }
    }

    /**
     * 扫描TRON链指定区块范围
     */
    public ScanResult scanTronBlockRange(BigInteger fromBlock, BigInteger toBlock, int timeoutSeconds) {
        try {
            log.info("开始扫描TRON链区块范围: {} - {}", fromBlock, toBlock);

            if (!tronConfigFacade.isEnabled()) {
                return ScanResult.failure("TRON链配置未启用");
            }

            // 验证区块范围
            if (fromBlock.compareTo(toBlock) > 0) {
                return ScanResult.failure("起始区块号不能大于结束区块号");
            }

            long blockCount = toBlock.subtract(fromBlock).longValue() + 1;
            if (blockCount > 100) {
                return ScanResult.failure("扫描区块数量不能超过100个，当前: " + blockCount);
            }

            // 执行TRON扫描任务
            return executeTronScan(fromBlock, toBlock, timeoutSeconds);

        } catch (Exception e) {
            log.error("TRON链扫描区块范围{} - {}失败", fromBlock, toBlock, e);
            return ScanResult.failure("扫描失败: " + e.getMessage());
        }
    }

    // ============ 工具方法和支持功能 ============

    /**
     * 获取支持的EVM链列表
     */
    public List<String> getSupportedEvmChains() {
        return evmConfigFacadeMap.keySet().stream()
            .filter(chainName -> evmConfigFacadeMap.get(chainName).isEnabled())
            .sorted()
            .collect(Collectors.toList());
    }

    /**
     * 检查链是否支持
     */
    public boolean isChainSupported(String chainName) {
        if ("TRON".equalsIgnoreCase(chainName)) {
            return tronConfigFacade.isEnabled();
        }
        EvmConfigFacade configFacade = evmConfigFacadeMap.get(chainName);
        return configFacade != null && configFacade.isEnabled();
    }

    /**
     * 手动保存指定链的当前扫描进度
     */
    public boolean saveCurrentScanProgress(String chainName) {
        try {
            MagicianBlockchainScan scanTask = scanTasks.get(chainName);
            if (scanTask != null) {
                BigInteger currentBlock = scanTask.getCurrentBlockHeight();
                if (currentBlock != null) {
                    saveLastScanBlock(chainName, currentBlock);
                    log.info("手动保存{}链扫描进度: {}", chainName, currentBlock);
                    return true;
                }
            }
            log.warn("{}链扫描任务未运行或无法获取当前区块高度", chainName);
            return false;
        } catch (Exception e) {
            log.error("手动保存{}链扫描进度失败: {}", chainName, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 获取指定链的扫描进度信息
     */
    public ScanProgressInfo getScanProgressInfo(String chainName) {
        try {
            // 从Redis获取保存的进度
            BigInteger savedBlock = getLastScanBlock(chainName);

            // 获取当前运行中的进度
            BigInteger currentBlock = null;
            boolean isScanning = isChainScanning(chainName);
            if (isScanning) {
                MagicianBlockchainScan scanTask = scanTasks.get(chainName);
                if (scanTask != null) {
                    currentBlock = scanTask.getCurrentBlockHeight();
                }
            }

            return new ScanProgressInfo(chainName, savedBlock, currentBlock, isScanning);
        } catch (Exception e) {
            log.error("获取{}链扫描进度信息失败: {}", chainName, e.getMessage(), e);
            return new ScanProgressInfo(chainName, null, null, false);
        }
    }

    // ============ 私有辅助方法 ============

    /**
     * 创建EVM RPC初始化配置
     */
    private EthRpcInit createEthRpcInit(EvmConfigFacade configFacade) {
        try {
            EthRpcInit ethRpcInit = EthRpcInit.create();

            // {{ AURA-X: Modify - 设置自定义链名称，确保BSC、ARB、BASE等链能正确识别. Approval: 寸止(ID:1678886403). }}
            // 设置自定义链名称
            ethRpcInit.setCustomChainName(configFacade.getChainName());

            // 添加主要RPC端点
            String primaryEndpoint = configFacade.getPrimaryEndpoint();
            if (primaryEndpoint != null && !primaryEndpoint.trim().isEmpty()) {
                ethRpcInit.addRpcUrl(primaryEndpoint);
                log.debug("{}链添加主要RPC端点: {}", configFacade.getChainName(), primaryEndpoint);
            }

//            // 添加备用RPC端点
//            String backupEndpoint = configFacade.getBackupEndpoint();
//            if (backupEndpoint != null && !backupEndpoint.trim().isEmpty()) {
//                ethRpcInit.addRpcUrl(backupEndpoint);
//                log.debug("{}链添加备用RPC端点: {}", configFacade.getChainName(), backupEndpoint);
//            }

            return ethRpcInit;
        } catch (Exception e) {
            log.error("{}链创建RPC配置失败", configFacade.getChainName(), e);
            throw new RuntimeException("创建" + configFacade.getChainName() + "链RPC配置失败", e);
        }
    }

    /**
     * 创建TRON RPC初始化配置
     */
    private TronRpcInit createTronRpcInit() {
        try {
            TronRpcInit tronRpcInit = TronRpcInit.create();

            // 添加主要RPC端点
            String primaryEndpoint = tronConfigFacade.getPrimaryEndpoint();
            if (primaryEndpoint != null && !primaryEndpoint.trim().isEmpty()) {
                tronRpcInit.addRpcUrl(primaryEndpoint);
                log.debug("TRON链添加主要RPC端点: {}", primaryEndpoint);
            }

//            // 添加备用RPC端点
//            String backupEndpoint = tronConfigFacade.getBackupEndpoint();
//            if (backupEndpoint != null && !backupEndpoint.trim().isEmpty()) {
//                tronRpcInit.addRpcUrl(backupEndpoint);
//                log.debug("TRON链添加备用RPC端点: {}", backupEndpoint);
//            }

            return tronRpcInit;
        } catch (Exception e) {
            log.error("TRON链创建RPC配置失败", e);
            throw new RuntimeException("创建TRON链RPC配置失败", e);
        }
    }

    /**
     * 保存扫描进度到Redis
     */
    private void saveLastScanBlock(String chainName, BigInteger blockNumber) {
        try {
            String redisKey = chainName.toLowerCase() + "_current_block";
            RBucket<BigInteger> bucket = RedisUtils.getClient().getBucket(redisKey);
            bucket.set(blockNumber);
            log.trace("保存{}链扫描进度到Redis: {} -> {}", chainName, redisKey, blockNumber);
        } catch (Exception e) {
            log.error("保存{}链扫描进度到Redis失败: {}", chainName, e.getMessage(), e);
        }
    }

    /**
     * 从Redis获取扫描进度
     */
    private BigInteger getLastScanBlock(String chainName) {
        try {
            String redisKey = chainName.toLowerCase() + "_current_block";
            RBucket<BigInteger> bucket = RedisUtils.getClient().getBucket(redisKey);
            BigInteger lastBlock = bucket.get();
            if (lastBlock != null) {
                log.trace("从Redis获取{}链扫描进度: {} -> {}", chainName, redisKey, lastBlock);
            }
            return lastBlock;
        } catch (Exception e) {
            log.error("从Redis获取{}链扫描进度失败: {}", chainName, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 执行EVM链手动扫描
     */
    private ScanResult executeEvmScan(EvmConfigFacade configFacade, BigInteger fromBlock, BigInteger toBlock, int timeoutSeconds) {
        long startTime = System.currentTimeMillis();

        try {
            // 创建RPC配置
            EthRpcInit rpcInit = createEthRpcInit(configFacade);

            // 创建独立的扫描器实例（不包含进度保存事件）
            MagicianBlockchainScan scanner = MagicianBlockchainScan.create()
                .setRpcUrl(rpcInit)
                .setBeginBlockNumber(fromBlock)
                .setEndBlockNumber(toBlock)
                .setScanPeriod(1000)
                .enableLogScanning()
                // 按处理顺序添加监控事件（不包含进度保存事件）
                .addEthMonitorEvent(evmAddressFilterEvent)      // 1. 地址预过滤
                .addEthMonitorEvent(evmEventFilterEvent)        // 2. 事件过滤
                .addEthMonitorEvent(evmBusinessProcessEvent);   // 3. 业务处理

            // 启动扫描
            scanner.start();


            long duration = System.currentTimeMillis() - startTime;
            String message = String.format("手动扫描已启动，区块范围: %s - %s，耗时: %dms",
                fromBlock, toBlock, duration);

            log.info("{}链{}", configFacade.getChainName(), message);
            return ScanResult.success(message);


        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            log.error("{}链创建手动扫描任务失败，耗时: {}ms", configFacade.getChainName(), duration, e);
            return ScanResult.failure("创建手动扫描任务失败: " + e.getMessage());
        }
    }

    /**
     * 执行TRON链手动扫描
     */
    private ScanResult executeTronScan(BigInteger fromBlock, BigInteger toBlock, int timeoutSeconds) {
        long startTime = System.currentTimeMillis();

        // 创建TRON RPC配置
        TronRpcInit rpcInit = createTronRpcInit();

        // 创建独立的TRON扫描器实例（不包含进度保存事件）
        MagicianBlockchainScan scanner = MagicianBlockchainScan.create()
            .setRpcUrl(rpcInit)
            .setBeginBlockNumber(fromBlock.subtract(new BigInteger("1")))
            .setEndBlockNumber(toBlock)
            .setScanPeriod(1000) // 1秒扫描周期，快速完成
            // 按处理顺序添加监控事件（不包含进度保存事件）
            .addTronMonitorEvent(tronAddressFilterEvent)      // 1. 地址预过滤
            .addTronMonitorEvent(tronTransactionDetailEvent)  // 2. 获取交易详情
            .addTronMonitorEvent(tronEventFilterEvent)        // 3. 事件过滤
            .addTronMonitorEvent(tronBusinessProcessEvent);   // 4. 业务处理
        try {
            scanner.start();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        long duration = System.currentTimeMillis() - startTime;
        String message = String.format("TRON链手动扫描已启动，区块范围: %s - %s，耗时: %dms",
            fromBlock, toBlock, duration);

        log.info(message);
        return ScanResult.success(message);

    }

    /**
     * 通过交易哈希获取区块号
     */
    private BigInteger getBlockNumberByTxHash(String txHash, EvmConfigFacade configFacade) {
        try {
            // 通过交易回执获取区块号
            org.web3j.protocol.core.methods.response.TransactionReceipt receipt =
                evmHelper.getTransactionReceipt(txHash, configFacade);
            if (receipt != null) {
                return receipt.getBlockNumber();
            }
            return null;
        } catch (Exception e) {
            // {{ AURA-X: Modify - 简化错误日志，不打印完整异常堆栈，只记录关键错误信息. Approval: 寸止(ID:1678886401). }}
            log.error("{}链获取交易{}的区块号失败: {}", configFacade.getChainName(), txHash, e.getMessage());
            return null;
        }
    }

    /**
     * 通过交易ID获取TRON区块号
     */
    private BigInteger getTronBlockNumberByTxId(String txId) {
        try {
            log.debug("开始获取TRON交易{}的区块号", txId);

            // 使用TronHttpApiHelper获取交易详细信息
            com.fasterxml.jackson.databind.JsonNode transactionInfo = tronHttpApiHelper.getTransactionInfoById(txId);

            if (transactionInfo == null) {
                log.warn("TRON交易{}不存在或未确认", txId);
                return null;
            }

            // 检查交易是否成功
            if (transactionInfo.has("result") &&
                !"SUCCESS".equals(transactionInfo.get("result").asText())) {
                log.warn("TRON交易{}执行失败，result: {}", txId,
                    transactionInfo.get("result").asText());
                return null;
            }

            // 提取区块号
            if (transactionInfo.has("blockNumber")) {
                long blockNumber = transactionInfo.get("blockNumber").asLong();
                log.debug("TRON交易{}位于区块: {}", txId, blockNumber);
                return BigInteger.valueOf(blockNumber);
            } else {
                log.warn("TRON交易{}信息中缺少blockNumber字段", txId);
                return null;
            }

        } catch (Exception e) {
            log.error("TRON链获取交易{}的区块号失败: {}", txId, e.getMessage(), e);
            return null;
        }
    }

    // ============ 内部类定义 ============

    /**
     * 扫描结果类
     */
    @Getter
    public static class ScanResult {
        private final boolean success;
        private final String message;
        private final long timestamp;

        private ScanResult(boolean success, String message) {
            this.success = success;
            this.message = message;
            this.timestamp = System.currentTimeMillis();
        }

        public static ScanResult success(String message) {
            return new ScanResult(true, message);
        }

        public static ScanResult failure(String message) {
            return new ScanResult(false, message);
        }

        @Override
        public String toString() {
            return String.format("ScanResult{success=%s, message='%s', timestamp=%d}",
                success, message, timestamp);
        }
    }

    /**
     * 扫描进度信息
     */
    public record ScanProgressInfo(@Getter String chainName, @Getter BigInteger savedBlock,
                                   @Getter BigInteger currentBlock, boolean isScanning) {
    }
}
